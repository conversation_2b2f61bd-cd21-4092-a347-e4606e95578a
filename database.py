import sqlite3
import datetime
from typing import List, Dict, Optional, Tuple

class DatabaseManager:
    def __init__(self, db_path: str = "knowledge_base.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化数据库表"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建分类表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建知识条目表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS knowledge_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                category_id INTEGER,
                title TEXT NOT NULL,
                content TEXT NOT NULL,
                other_info TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIG<PERSON> KEY (category_id) REFERENCES categories (id)
            )
        ''')
        
        # 创建学习记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS learning_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                item_id INTEGER,
                status INTEGER DEFAULT 0,  -- 0:不会, 1:模糊, 2:已学会
                last_review TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                next_review TIMESTAMP,
                review_count INTEGER DEFAULT 0,
                FOREIGN KEY (item_id) REFERENCES knowledge_items (id)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def add_category(self, name: str) -> int:
        """添加分类"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        try:
            cursor.execute("INSERT INTO categories (name) VALUES (?)", (name,))
            category_id = cursor.lastrowid
            conn.commit()
            return category_id
        except sqlite3.IntegrityError:
            # 分类已存在，返回现有ID
            cursor.execute("SELECT id FROM categories WHERE name = ?", (name,))
            return cursor.fetchone()[0]
        finally:
            conn.close()
    
    def get_categories(self) -> List[Dict]:
        """获取所有分类"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT id, name FROM categories ORDER BY name")
        categories = [{"id": row[0], "name": row[1]} for row in cursor.fetchall()]
        conn.close()
        return categories
    
    def add_knowledge_item(self, category_name: str, title: str, content: str, other_info: str = "") -> int:
        """添加知识条目"""
        category_id = self.add_category(category_name)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO knowledge_items (category_id, title, content, other_info)
            VALUES (?, ?, ?, ?)
        ''', (category_id, title, content, other_info))
        
        item_id = cursor.lastrowid
        
        # 创建学习记录
        next_review = datetime.datetime.now() + datetime.timedelta(days=1)
        cursor.execute('''
            INSERT INTO learning_records (item_id, next_review)
            VALUES (?, ?)
        ''', (item_id, next_review))
        
        conn.commit()
        conn.close()
        return item_id
    
    def get_knowledge_items(self, category_id: Optional[int] = None, order_by: str = "created_at") -> List[Dict]:
        """获取知识条目"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        if category_id:
            query = '''
                SELECT ki.id, ki.title, ki.content, ki.other_info, ki.created_at, c.name as category
                FROM knowledge_items ki
                JOIN categories c ON ki.category_id = c.id
                WHERE ki.category_id = ?
                ORDER BY ki.{} DESC
            '''.format(order_by)
            cursor.execute(query, (category_id,))
        else:
            query = '''
                SELECT ki.id, ki.title, ki.content, ki.other_info, ki.created_at, c.name as category
                FROM knowledge_items ki
                JOIN categories c ON ki.category_id = c.id
                ORDER BY ki.{} DESC
            '''.format(order_by)
            cursor.execute(query)
        
        items = []
        for row in cursor.fetchall():
            items.append({
                "id": row[0],
                "title": row[1],
                "content": row[2],
                "other_info": row[3],
                "created_at": row[4],
                "category": row[5]
            })
        
        conn.close()
        return items
    
    def search_items(self, keyword: str) -> List[Dict]:
        """搜索知识条目"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        query = '''
            SELECT ki.id, ki.title, ki.content, ki.other_info, ki.created_at, c.name as category
            FROM knowledge_items ki
            JOIN categories c ON ki.category_id = c.id
            WHERE ki.title LIKE ? OR ki.content LIKE ? OR ki.other_info LIKE ?
            ORDER BY ki.created_at DESC
        '''
        search_term = f"%{keyword}%"
        cursor.execute(query, (search_term, search_term, search_term))
        
        items = []
        for row in cursor.fetchall():
            items.append({
                "id": row[0],
                "title": row[1],
                "content": row[2],
                "other_info": row[3],
                "created_at": row[4],
                "category": row[5]
            })
        
        conn.close()
        return items
    
    def get_random_item(self) -> Optional[Dict]:
        """获取随机知识条目"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT ki.id, ki.title, ki.content, ki.other_info, c.name as category
            FROM knowledge_items ki
            JOIN categories c ON ki.category_id = c.id
            ORDER BY RANDOM()
            LIMIT 1
        ''')
        
        row = cursor.fetchone()
        conn.close()
        
        if row:
            return {
                "id": row[0],
                "title": row[1],
                "content": row[2],
                "other_info": row[3],
                "category": row[4]
            }
        return None
    
    def update_learning_status(self, item_id: int, status: int):
        """更新学习状态"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 计算下次复习时间（艾宾浩斯曲线）
        intervals = {0: 1, 1: 3, 2: 7}  # 不会:1天, 模糊:3天, 已学会:7天
        next_review = datetime.datetime.now() + datetime.timedelta(days=intervals.get(status, 1))
        
        cursor.execute('''
            UPDATE learning_records 
            SET status = ?, last_review = CURRENT_TIMESTAMP, next_review = ?, review_count = review_count + 1
            WHERE item_id = ?
        ''', (status, next_review, item_id))
        
        conn.commit()
        conn.close()
    
    def get_items_for_review(self) -> List[Dict]:
        """获取需要复习的条目"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT ki.id, ki.title, ki.content, ki.other_info, c.name as category, lr.status
            FROM knowledge_items ki
            JOIN categories c ON ki.category_id = c.id
            JOIN learning_records lr ON ki.id = lr.item_id
            WHERE lr.next_review <= CURRENT_TIMESTAMP
            ORDER BY lr.next_review
        ''')
        
        items = []
        for row in cursor.fetchall():
            items.append({
                "id": row[0],
                "title": row[1],
                "content": row[2],
                "other_info": row[3],
                "category": row[4],
                "status": row[5]
            })
        
        conn.close()
        return items
