import sqlite3
import datetime
from typing import List, Dict, Optional, Tuple

class DatabaseManager:
    def __init__(self, db_path: str = "knowledge_base.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化数据库表"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建分类表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建知识条目表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS knowledge_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                content TEXT NOT NULL,
                other_info TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 创建知识条目与分类的关联表（多对多关系）
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS item_categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                item_id INTEGER,
                category_id INTEGER,
                FOREIGN KEY (item_id) REFERENCES knowledge_items (id) ON DELETE CASCADE,
                FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE CASCADE,
                UNIQUE(item_id, category_id)
            )
        ''')
        
        # 创建学习记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS learning_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                item_id INTEGER,
                status INTEGER DEFAULT 0,  -- 0:不会, 1:模糊, 2:已学会
                last_review TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                next_review TIMESTAMP,
                review_count INTEGER DEFAULT 0,
                FOREIGN KEY (item_id) REFERENCES knowledge_items (id)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def add_category(self, name: str) -> int:
        """添加分类"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        try:
            cursor.execute("INSERT INTO categories (name) VALUES (?)", (name,))
            category_id = cursor.lastrowid
            conn.commit()
            return category_id
        except sqlite3.IntegrityError:
            # 分类已存在，返回现有ID
            cursor.execute("SELECT id FROM categories WHERE name = ?", (name,))
            return cursor.fetchone()[0]
        finally:
            conn.close()
    
    def get_categories(self) -> List[Dict]:
        """获取所有分类"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT id, name FROM categories ORDER BY name")
        categories = [{"id": row[0], "name": row[1]} for row in cursor.fetchall()]
        conn.close()
        return categories
    
    def add_knowledge_item(self, category_names: str, title: str, content: str, other_info: str = "") -> int:
        """添加知识条目（支持多分类，用逗号分隔）"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 插入知识条目
        cursor.execute('''
            INSERT INTO knowledge_items (title, content, other_info)
            VALUES (?, ?, ?)
        ''', (title, content, other_info))

        item_id = cursor.lastrowid

        # 处理多个分类
        categories = [cat.strip() for cat in category_names.split(',') if cat.strip()]
        for category_name in categories:
            category_id = self.add_category(category_name)
            # 添加条目与分类的关联
            cursor.execute('''
                INSERT OR IGNORE INTO item_categories (item_id, category_id)
                VALUES (?, ?)
            ''', (item_id, category_id))

        # 创建学习记录
        next_review = datetime.datetime.now() + datetime.timedelta(days=1)
        cursor.execute('''
            INSERT INTO learning_records (item_id, next_review)
            VALUES (?, ?)
        ''', (item_id, next_review))

        conn.commit()
        conn.close()
        return item_id
    
    def get_knowledge_items(self, category_ids: Optional[List[int]] = None, order_by: str = "created_at") -> List[Dict]:
        """获取知识条目（支持多分类筛选）"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        if category_ids:
            # 获取指定分类的条目
            placeholders = ','.join('?' * len(category_ids))
            query = f'''
                SELECT DISTINCT ki.id, ki.title, ki.content, ki.other_info, ki.created_at
                FROM knowledge_items ki
                JOIN item_categories ic ON ki.id = ic.item_id
                WHERE ic.category_id IN ({placeholders})
                ORDER BY ki.{order_by} DESC
            '''
            cursor.execute(query, category_ids)
        else:
            # 获取所有条目
            query = f'''
                SELECT ki.id, ki.title, ki.content, ki.other_info, ki.created_at
                FROM knowledge_items ki
                ORDER BY ki.{order_by} DESC
            '''
            cursor.execute(query)

        items = []
        for row in cursor.fetchall():
            item_id = row[0]
            # 获取该条目的所有分类
            cursor.execute('''
                SELECT c.name FROM categories c
                JOIN item_categories ic ON c.id = ic.category_id
                WHERE ic.item_id = ?
                ORDER BY c.name
            ''', (item_id,))
            categories = [cat_row[0] for cat_row in cursor.fetchall()]

            items.append({
                "id": row[0],
                "title": row[1],
                "content": row[2],
                "other_info": row[3],
                "created_at": row[4],
                "categories": categories,
                "category": ", ".join(categories)  # 兼容性字段
            })

        conn.close()
        return items
    
    def search_items(self, keyword: str) -> List[Dict]:
        """搜索知识条目"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        query = '''
            SELECT DISTINCT ki.id, ki.title, ki.content, ki.other_info, ki.created_at
            FROM knowledge_items ki
            WHERE ki.title LIKE ? OR ki.content LIKE ? OR ki.other_info LIKE ?
            ORDER BY ki.created_at DESC
        '''
        search_term = f"%{keyword}%"
        cursor.execute(query, (search_term, search_term, search_term))

        items = []
        for row in cursor.fetchall():
            item_id = row[0]
            # 获取该条目的所有分类
            cursor.execute('''
                SELECT c.name FROM categories c
                JOIN item_categories ic ON c.id = ic.category_id
                WHERE ic.item_id = ?
                ORDER BY c.name
            ''', (item_id,))
            categories = [cat_row[0] for cat_row in cursor.fetchall()]

            items.append({
                "id": row[0],
                "title": row[1],
                "content": row[2],
                "other_info": row[3],
                "created_at": row[4],
                "categories": categories,
                "category": ", ".join(categories)
            })

        conn.close()
        return items

    def delete_knowledge_item(self, item_id: int) -> bool:
        """删除知识条目"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            # 删除学习记录
            cursor.execute("DELETE FROM learning_records WHERE item_id = ?", (item_id,))

            # 删除分类关联
            cursor.execute("DELETE FROM item_categories WHERE item_id = ?", (item_id,))

            # 删除知识条目
            cursor.execute("DELETE FROM knowledge_items WHERE id = ?", (item_id,))

            conn.commit()
            return True
        except Exception as e:
            conn.rollback()
            print(f"删除失败: {e}")
            return False
        finally:
            conn.close()

    def delete_multiple_items(self, item_ids: List[int]) -> int:
        """批量删除知识条目，返回成功删除的数量"""
        success_count = 0
        for item_id in item_ids:
            if self.delete_knowledge_item(item_id):
                success_count += 1
        return success_count

    def get_categories_by_item_id(self, item_id: int) -> List[str]:
        """获取指定条目的所有分类"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT c.name FROM categories c
            JOIN item_categories ic ON c.id = ic.category_id
            WHERE ic.item_id = ?
            ORDER BY c.name
        ''', (item_id,))

        categories = [row[0] for row in cursor.fetchall()]
        conn.close()
        return categories
    
    def get_random_item(self) -> Optional[Dict]:
        """获取随机知识条目"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT ki.id, ki.title, ki.content, ki.other_info, c.name as category
            FROM knowledge_items ki
            JOIN categories c ON ki.category_id = c.id
            ORDER BY RANDOM()
            LIMIT 1
        ''')
        
        row = cursor.fetchone()
        conn.close()
        
        if row:
            return {
                "id": row[0],
                "title": row[1],
                "content": row[2],
                "other_info": row[3],
                "category": row[4]
            }
        return None
    
    def update_learning_status(self, item_id: int, status: int):
        """更新学习状态"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 计算下次复习时间（艾宾浩斯曲线）
        intervals = {0: 1, 1: 3, 2: 7}  # 不会:1天, 模糊:3天, 已学会:7天
        next_review = datetime.datetime.now() + datetime.timedelta(days=intervals.get(status, 1))
        
        cursor.execute('''
            UPDATE learning_records 
            SET status = ?, last_review = CURRENT_TIMESTAMP, next_review = ?, review_count = review_count + 1
            WHERE item_id = ?
        ''', (status, next_review, item_id))
        
        conn.commit()
        conn.close()
    
    def get_items_for_review(self) -> List[Dict]:
        """获取需要复习的条目"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT ki.id, ki.title, ki.content, ki.other_info, c.name as category, lr.status
            FROM knowledge_items ki
            JOIN categories c ON ki.category_id = c.id
            JOIN learning_records lr ON ki.id = lr.item_id
            WHERE lr.next_review <= CURRENT_TIMESTAMP
            ORDER BY lr.next_review
        ''')
        
        items = []
        for row in cursor.fetchall():
            items.append({
                "id": row[0],
                "title": row[1],
                "content": row[2],
                "other_info": row[3],
                "category": row[4],
                "status": row[5]
            })
        
        conn.close()
        return items
