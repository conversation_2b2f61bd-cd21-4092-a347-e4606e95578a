# 智能知识管理系统

一个功能完整的知识管理和学习软件，支持知识分类、可视化展示、智能复习等功能。

## 功能特性

### 📝 知识输入管理
- **固定输入框**：分类、题目、内容、其他信息
- **智能分类**：历史分类自动作为选项，支持新增分类
- **数据持久化**：使用SQLite数据库本地存储

### 📊 交互式数据可视化
- **交互式网点图**：展示知识点之间的关联关系，点击节点查看详情
- **交互式思维导图**：层次化展示知识结构，支持节点点击交互
- **分类分布图**：饼图显示各分类的数量分布
- **鼠标悬停效果**：悬停在节点上显示预览信息

### 📋 数据浏览与管理
- **多种排序**：按创建时间、更新时间排序
- **分类筛选**：可按分类查看特定内容
- **详情查看**：双击查看完整知识详情

### 🔍 搜索功能
- **全文搜索**：在标题、内容、其他信息中搜索
- **高亮匹配**：显示匹配内容的上下文
- **快速定位**：双击搜索结果查看详情

### 🎯 智能学习系统
- **随机练习**：随机展示题目进行练习
- **答案查看**：点击显示完整答案
- **学习状态**：标记"不会"、"模糊"、"已学会"
- **艾宾浩斯复习**：基于遗忘曲线的智能复习提醒

## 安装和运行

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 运行程序
```bash
# 直接运行主程序
python main.py

# 或者运行测试脚本（会自动添加示例数据）
python test_app.py
```

## 使用说明

### 添加知识
1. 在左侧输入区域填写分类、题目、内容等信息
2. 点击"保存知识"按钮保存
3. 系统会自动记住分类，下次输入时可直接选择

### 查看知识
1. 切换到"知识列表"选项卡
2. 可以按时间或分类进行排序和筛选
3. 双击任意条目查看详细信息

### 交互式可视化展示
1. 切换到"可视化"选项卡
2. 点击"网点图"、"思维导图"或"分类分布"按钮
3. 系统会生成相应的可视化图表
4. **交互功能**：
   - 在网点图和思维导图中，鼠标悬停在节点上可看到预览信息
   - 点击知识点节点可直接跳转到详情页面
   - 鼠标悬停时会显示手型光标提示可点击

### 练习模式
1. 切换到"练习模式"选项卡
2. 点击"随机题目"开始练习
3. 查看题目后可点击"显示答案"
4. 根据掌握程度选择"不会"、"模糊"或"已学会"

### 复习提醒
1. 在练习模式中点击"复习提醒"
2. 系统会显示需要复习的内容
3. 复习时间基于艾宾浩斯遗忘曲线计算

### 搜索功能
1. 切换到"搜索"选项卡
2. 输入关键词点击"搜索"
3. 双击搜索结果查看详情

## 技术架构

- **GUI框架**：CustomTkinter（现代化界面）
- **数据库**：SQLite（轻量级本地存储）
- **可视化**：Matplotlib + NetworkX
- **语言**：Python 3.7+

## 文件结构

```
├── main.py              # 主程序入口
├── database.py          # 数据库管理模块
├── visualization.py     # 交互式可视化模块
├── test_app.py          # 测试脚本（含示例数据）
├── requirements.txt     # 依赖包列表
├── README.md           # 说明文档
└── knowledge_base.db   # 数据库文件（运行后自动生成）
```

## 艾宾浩斯复习算法

系统根据学习状态自动计算下次复习时间：
- **不会**：1天后复习
- **模糊**：3天后复习  
- **已学会**：7天后复习

## 注意事项

1. 首次运行会自动创建数据库文件
2. 所有数据保存在本地，请注意备份
3. 可视化功能需要一定的数据量才能有较好的展示效果
4. 建议定期使用练习模式巩固学习效果

## 更新日志

### v1.0.0
- 实现基础的知识输入和管理功能
- 添加交互式可视化展示功能（支持节点点击和悬停）
- 实现搜索和练习模式
- 集成艾宾浩斯复习算法
- 提供测试脚本和示例数据
