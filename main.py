import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox, ttk
from database import DatabaseManager
from visualization import VisualizationManager
import random
from datetime import datetime

class KnowledgeBaseApp:
    def __init__(self):
        # 设置主题
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")

        # 初始化数据库和可视化管理器
        self.db = DatabaseManager()
        self.viz = VisualizationManager()

        # 创建主窗口
        self.root = ctk.CTk()
        self.root.title("智能知识管理系统")
        self.root.geometry("1200x800")

        # 创建界面
        self.create_widgets()
        self.refresh_categories()
        self.refresh_items_list()

    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ctk.CTkFrame(self.root)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # 左侧输入区域
        left_frame = ctk.CTkFrame(main_frame)
        left_frame.pack(side="left", fill="y", padx=(0, 5))

        # 输入区域标题
        input_title = ctk.CTkLabel(left_frame, text="知识输入", font=ctk.CTkFont(size=18, weight="bold"))
        input_title.pack(pady=(10, 20))

        # 分类输入
        ctk.CTkLabel(left_frame, text="分类:", font=ctk.CTkFont(size=14)).pack(anchor="w", padx=20)
        self.category_var = tk.StringVar()
        self.category_combo = ctk.CTkComboBox(left_frame, variable=self.category_var, width=250)
        self.category_combo.pack(pady=(5, 15), padx=20)

        # 题目输入
        ctk.CTkLabel(left_frame, text="题目:", font=ctk.CTkFont(size=14)).pack(anchor="w", padx=20)
        self.title_entry = ctk.CTkEntry(left_frame, width=250, height=35)
        self.title_entry.pack(pady=(5, 15), padx=20)

        # 内容输入
        ctk.CTkLabel(left_frame, text="内容:", font=ctk.CTkFont(size=14)).pack(anchor="w", padx=20)
        self.content_text = ctk.CTkTextbox(left_frame, width=250, height=150)
        self.content_text.pack(pady=(5, 15), padx=20)

        # 其他信息输入
        ctk.CTkLabel(left_frame, text="其他信息:", font=ctk.CTkFont(size=14)).pack(anchor="w", padx=20)
        self.other_text = ctk.CTkTextbox(left_frame, width=250, height=80)
        self.other_text.pack(pady=(5, 15), padx=20)

        # 保存按钮
        save_btn = ctk.CTkButton(left_frame, text="保存知识", command=self.save_knowledge,
                                font=ctk.CTkFont(size=14, weight="bold"), height=40)
        save_btn.pack(pady=20, padx=20)

        # 右侧主要功能区域
        right_frame = ctk.CTkFrame(main_frame)
        right_frame.pack(side="right", fill="both", expand=True, padx=(5, 0))

        # 创建选项卡
        self.notebook = ctk.CTkTabview(right_frame)
        self.notebook.pack(fill="both", expand=True, padx=10, pady=10)

        # 知识列表选项卡
        self.create_list_tab()

        # 可视化选项卡
        self.create_visualization_tab()

        # 练习选项卡
        self.create_practice_tab()

        # 搜索选项卡
        self.create_search_tab()

    def create_list_tab(self):
        """创建知识列表选项卡"""
        list_tab = self.notebook.add("知识列表")

        # 控制按钮框架
        control_frame = ctk.CTkFrame(list_tab)
        control_frame.pack(fill="x", padx=10, pady=(10, 5))

        # 排序选择
        ctk.CTkLabel(control_frame, text="排序方式:").pack(side="left", padx=(10, 5))
        self.sort_var = tk.StringVar(value="created_at")
        sort_combo = ctk.CTkComboBox(control_frame, variable=self.sort_var,
                                   values=["created_at", "updated_at"], width=120)
        sort_combo.pack(side="left", padx=5)

        # 分类筛选
        ctk.CTkLabel(control_frame, text="分类筛选:").pack(side="left", padx=(20, 5))
        self.filter_var = tk.StringVar(value="全部")
        self.filter_combo = ctk.CTkComboBox(control_frame, variable=self.filter_var, width=120)
        self.filter_combo.pack(side="left", padx=5)

        # 刷新按钮
        refresh_btn = ctk.CTkButton(control_frame, text="刷新", command=self.refresh_items_list, width=80)
        refresh_btn.pack(side="left", padx=(20, 10))

        # 知识列表
        list_frame = ctk.CTkFrame(list_tab)
        list_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # 创建Treeview
        columns = ("ID", "分类", "题目", "创建时间")
        self.items_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)

        # 设置列标题
        for col in columns:
            self.items_tree.heading(col, text=col)
            self.items_tree.column(col, width=150)

        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.items_tree.yview)
        self.items_tree.configure(yscrollcommand=scrollbar.set)

        self.items_tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 绑定双击事件
        self.items_tree.bind("<Double-1>", self.show_item_detail)

    def create_visualization_tab(self):
        """创建可视化选项卡"""
        viz_tab = self.notebook.add("可视化")

        # 控制按钮
        control_frame = ctk.CTkFrame(viz_tab)
        control_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkButton(control_frame, text="网点图", command=self.show_network_graph).pack(side="left", padx=5)
        ctk.CTkButton(control_frame, text="思维导图", command=self.show_mind_map).pack(side="left", padx=5)
        ctk.CTkButton(control_frame, text="分类分布", command=self.show_distribution).pack(side="left", padx=5)

        # 可视化显示区域
        self.viz_frame = ctk.CTkFrame(viz_tab)
        self.viz_frame.pack(fill="both", expand=True, padx=10, pady=10)

    def create_practice_tab(self):
        """创建练习选项卡"""
        practice_tab = self.notebook.add("练习模式")

        # 控制区域
        control_frame = ctk.CTkFrame(practice_tab)
        control_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkButton(control_frame, text="随机题目", command=self.show_random_question).pack(side="left", padx=5)
        ctk.CTkButton(control_frame, text="复习提醒", command=self.show_review_items).pack(side="left", padx=5)

        # 题目显示区域
        self.question_frame = ctk.CTkFrame(practice_tab)
        self.question_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # 初始化显示
        welcome_label = ctk.CTkLabel(self.question_frame, text="点击'随机题目'开始练习",
                                   font=ctk.CTkFont(size=16))
        welcome_label.pack(expand=True)

    def create_search_tab(self):
        """创建搜索选项卡"""
        search_tab = self.notebook.add("搜索")

        # 搜索控制区域
        search_control = ctk.CTkFrame(search_tab)
        search_control.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(search_control, text="搜索关键词:").pack(side="left", padx=(10, 5))
        self.search_entry = ctk.CTkEntry(search_control, width=300)
        self.search_entry.pack(side="left", padx=5)

        search_btn = ctk.CTkButton(search_control, text="搜索", command=self.search_knowledge)
        search_btn.pack(side="left", padx=5)

        # 搜索结果显示
        self.search_frame = ctk.CTkFrame(search_tab)
        self.search_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # 搜索结果列表
        search_columns = ("ID", "分类", "题目", "匹配内容")
        self.search_tree = ttk.Treeview(self.search_frame, columns=search_columns, show="headings", height=20)

        for col in search_columns:
            self.search_tree.heading(col, text=col)
            self.search_tree.column(col, width=200)

        search_scrollbar = ttk.Scrollbar(self.search_frame, orient="vertical", command=self.search_tree.yview)
        self.search_tree.configure(yscrollcommand=search_scrollbar.set)

        self.search_tree.pack(side="left", fill="both", expand=True)
        search_scrollbar.pack(side="right", fill="y")

        self.search_tree.bind("<Double-1>", self.show_search_item_detail)

    def save_knowledge(self):
        """保存知识"""
        category = self.category_var.get().strip()
        title = self.title_entry.get().strip()
        content = self.content_text.get("1.0", "end-1c").strip()
        other = self.other_text.get("1.0", "end-1c").strip()

        if not category or not title or not content:
            messagebox.showerror("错误", "分类、题目和内容不能为空！")
            return

        try:
            self.db.add_knowledge_item(category, title, content, other)
            messagebox.showinfo("成功", "知识保存成功！")

            # 清空输入框
            self.title_entry.delete(0, "end")
            self.content_text.delete("1.0", "end")
            self.other_text.delete("1.0", "end")

            # 刷新界面
            self.refresh_categories()
            self.refresh_items_list()

        except Exception as e:
            messagebox.showerror("错误", f"保存失败：{str(e)}")

    def refresh_categories(self):
        """刷新分类列表"""
        categories = self.db.get_categories()
        category_names = [cat["name"] for cat in categories]

        self.category_combo.configure(values=category_names)

        # 更新筛选下拉框
        filter_values = ["全部"] + category_names
        self.filter_combo.configure(values=filter_values)

    def refresh_items_list(self):
        """刷新知识列表"""
        # 清空现有项目
        for item in self.items_tree.get_children():
            self.items_tree.delete(item)

        # 获取筛选条件
        filter_category = self.filter_var.get()
        category_id = None
        if filter_category != "全部":
            categories = self.db.get_categories()
            for cat in categories:
                if cat["name"] == filter_category:
                    category_id = cat["id"]
                    break

        # 获取排序方式
        order_by = self.sort_var.get()

        # 获取知识项目
        items = self.db.get_knowledge_items(category_id, order_by)

        # 添加到列表
        for item in items:
            created_at = datetime.fromisoformat(item["created_at"]).strftime("%Y-%m-%d %H:%M")
            self.items_tree.insert("", "end", values=(
                item["id"], item["category"], item["title"], created_at
            ))

    def show_item_detail(self, event):
        """显示知识详情"""
        selection = self.items_tree.selection()
        if not selection:
            return

        item_data = self.items_tree.item(selection[0])["values"]
        item_id = item_data[0]

        # 获取完整信息
        items = self.db.get_knowledge_items()
        item_detail = None
        for item in items:
            if item["id"] == item_id:
                item_detail = item
                break

        if item_detail:
            self.show_detail_window(item_detail)

    def show_detail_window(self, item):
        """显示详情窗口"""
        detail_window = ctk.CTkToplevel(self.root)
        detail_window.title(f"知识详情 - {item['title']}")
        detail_window.geometry("600x500")

        # 标题
        title_label = ctk.CTkLabel(detail_window, text=item["title"],
                                 font=ctk.CTkFont(size=18, weight="bold"))
        title_label.pack(pady=10)

        # 分类和时间
        info_frame = ctk.CTkFrame(detail_window)
        info_frame.pack(fill="x", padx=20, pady=5)

        ctk.CTkLabel(info_frame, text=f"分类: {item['category']}").pack(side="left", padx=10)
        created_time = datetime.fromisoformat(item["created_at"]).strftime("%Y-%m-%d %H:%M")
        ctk.CTkLabel(info_frame, text=f"创建时间: {created_time}").pack(side="right", padx=10)

        # 内容
        ctk.CTkLabel(detail_window, text="内容:", font=ctk.CTkFont(size=14, weight="bold")).pack(anchor="w", padx=20, pady=(20, 5))
        content_text = ctk.CTkTextbox(detail_window, height=200)
        content_text.pack(fill="both", expand=True, padx=20, pady=5)
        content_text.insert("1.0", item["content"])
        content_text.configure(state="disabled")

        # 其他信息
        if item["other_info"]:
            ctk.CTkLabel(detail_window, text="其他信息:", font=ctk.CTkFont(size=14, weight="bold")).pack(anchor="w", padx=20, pady=(10, 5))
            other_text = ctk.CTkTextbox(detail_window, height=80)
            other_text.pack(fill="x", padx=20, pady=5)
            other_text.insert("1.0", item["other_info"])
            other_text.configure(state="disabled")

    def show_network_graph(self):
        """显示网点图"""
        self.clear_viz_frame()
        items = self.db.get_knowledge_items()
        if items:
            canvas = self.viz.create_network_graph(items, self.viz_frame)
            canvas.get_tk_widget().pack(fill="both", expand=True)
        else:
            ctk.CTkLabel(self.viz_frame, text="暂无数据").pack(expand=True)

    def show_mind_map(self):
        """显示思维导图"""
        self.clear_viz_frame()
        items = self.db.get_knowledge_items()
        if items:
            canvas = self.viz.create_mind_map(items, self.viz_frame)
            canvas.get_tk_widget().pack(fill="both", expand=True)
        else:
            ctk.CTkLabel(self.viz_frame, text="暂无数据").pack(expand=True)

    def show_distribution(self):
        """显示分类分布"""
        self.clear_viz_frame()
        items = self.db.get_knowledge_items()
        if items:
            canvas = self.viz.show_category_distribution(items, self.viz_frame)
            canvas.get_tk_widget().pack(fill="both", expand=True)
        else:
            ctk.CTkLabel(self.viz_frame, text="暂无数据").pack(expand=True)

    def clear_viz_frame(self):
        """清空可视化框架"""
        for widget in self.viz_frame.winfo_children():
            widget.destroy()

    def show_random_question(self):
        """显示随机题目"""
        self.clear_question_frame()

        item = self.db.get_random_item()
        if not item:
            ctk.CTkLabel(self.question_frame, text="暂无题目", font=ctk.CTkFont(size=16)).pack(expand=True)
            return

        # 题目显示
        question_label = ctk.CTkLabel(self.question_frame, text=f"题目: {item['title']}",
                                    font=ctk.CTkFont(size=18, weight="bold"))
        question_label.pack(pady=20)

        category_label = ctk.CTkLabel(self.question_frame, text=f"分类: {item['category']}",
                                    font=ctk.CTkFont(size=14))
        category_label.pack(pady=5)

        # 按钮框架
        button_frame = ctk.CTkFrame(self.question_frame)
        button_frame.pack(pady=20)

        # 显示答案按钮
        show_answer_btn = ctk.CTkButton(button_frame, text="显示答案",
                                      command=lambda: self.show_answer(item))
        show_answer_btn.pack(side="left", padx=5)

        # 学习状态按钮
        status_frame = ctk.CTkFrame(self.question_frame)
        status_frame.pack(pady=10)

        ctk.CTkLabel(status_frame, text="学习状态:", font=ctk.CTkFont(size=14)).pack()

        btn_frame = ctk.CTkFrame(status_frame)
        btn_frame.pack(pady=10)

        ctk.CTkButton(btn_frame, text="不会", fg_color="red",
                     command=lambda: self.update_status(item['id'], 0)).pack(side="left", padx=5)
        ctk.CTkButton(btn_frame, text="模糊", fg_color="orange",
                     command=lambda: self.update_status(item['id'], 1)).pack(side="left", padx=5)
        ctk.CTkButton(btn_frame, text="已学会", fg_color="green",
                     command=lambda: self.update_status(item['id'], 2)).pack(side="left", padx=5)

    def show_answer(self, item):
        """显示答案"""
        answer_window = ctk.CTkToplevel(self.root)
        answer_window.title("答案")
        answer_window.geometry("500x400")

        # 题目
        ctk.CTkLabel(answer_window, text=f"题目: {item['title']}",
                    font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)

        # 答案内容
        ctk.CTkLabel(answer_window, text="答案:", font=ctk.CTkFont(size=14, weight="bold")).pack(anchor="w", padx=20)
        answer_text = ctk.CTkTextbox(answer_window, height=200)
        answer_text.pack(fill="both", expand=True, padx=20, pady=10)
        answer_text.insert("1.0", item["content"])
        answer_text.configure(state="disabled")

        # 其他信息
        if item["other_info"]:
            ctk.CTkLabel(answer_window, text="其他信息:", font=ctk.CTkFont(size=14, weight="bold")).pack(anchor="w", padx=20)
            other_text = ctk.CTkTextbox(answer_window, height=80)
            other_text.pack(fill="x", padx=20, pady=10)
            other_text.insert("1.0", item["other_info"])
            other_text.configure(state="disabled")

    def update_status(self, item_id, status):
        """更新学习状态"""
        self.db.update_learning_status(item_id, status)
        status_names = {0: "不会", 1: "模糊", 2: "已学会"}
        messagebox.showinfo("状态更新", f"已标记为: {status_names[status]}")

    def clear_question_frame(self):
        """清空题目框架"""
        for widget in self.question_frame.winfo_children():
            widget.destroy()

    def show_review_items(self):
        """显示需要复习的项目"""
        review_items = self.db.get_items_for_review()

        if not review_items:
            messagebox.showinfo("复习提醒", "暂无需要复习的内容")
            return

        review_window = ctk.CTkToplevel(self.root)
        review_window.title("复习提醒")
        review_window.geometry("800x600")

        ctk.CTkLabel(review_window, text=f"需要复习的内容 ({len(review_items)}项)",
                    font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)

        # 复习列表
        review_columns = ("题目", "分类", "状态")
        review_tree = ttk.Treeview(review_window, columns=review_columns, show="headings", height=20)

        for col in review_columns:
            review_tree.heading(col, text=col)
            review_tree.column(col, width=200)

        for item in review_items:
            status_names = {0: "不会", 1: "模糊", 2: "已学会"}
            review_tree.insert("", "end", values=(
                item["title"], item["category"], status_names.get(item["status"], "未知")
            ))

        review_tree.pack(fill="both", expand=True, padx=20, pady=10)

    def search_knowledge(self):
        """搜索知识"""
        keyword = self.search_entry.get().strip()
        if not keyword:
            messagebox.showwarning("警告", "请输入搜索关键词")
            return

        # 清空搜索结果
        for item in self.search_tree.get_children():
            self.search_tree.delete(item)

        # 执行搜索
        results = self.db.search_items(keyword)

        # 显示结果
        for item in results:
            # 查找匹配的内容片段
            match_content = ""
            if keyword.lower() in item["title"].lower():
                match_content = item["title"]
            elif keyword.lower() in item["content"].lower():
                # 显示匹配内容的前后文
                content = item["content"]
                index = content.lower().find(keyword.lower())
                start = max(0, index - 20)
                end = min(len(content), index + len(keyword) + 20)
                match_content = "..." + content[start:end] + "..."
            elif keyword.lower() in item["other_info"].lower():
                match_content = item["other_info"][:50] + "..."

            self.search_tree.insert("", "end", values=(
                item["id"], item["category"], item["title"], match_content
            ))

        if not results:
            messagebox.showinfo("搜索结果", "未找到匹配的内容")

    def show_search_item_detail(self, event):
        """显示搜索结果详情"""
        selection = self.search_tree.selection()
        if not selection:
            return

        item_data = self.search_tree.item(selection[0])["values"]
        item_id = item_data[0]

        # 获取完整信息
        items = self.db.get_knowledge_items()
        item_detail = None
        for item in items:
            if item["id"] == item_id:
                item_detail = item
                break

        if item_detail:
            self.show_detail_window(item_detail)

    def run(self):
        """运行应用"""
        self.root.mainloop()

if __name__ == "__main__":
    app = KnowledgeBaseApp()
    app.run()