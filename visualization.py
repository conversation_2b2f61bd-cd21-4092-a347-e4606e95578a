import matplotlib.pyplot as plt
import networkx as nx
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import tkinter as tk
from typing import List, Dict, Callable, Optional
import matplotlib
matplotlib.use('TkAgg')

class VisualizationManager:
    def __init__(self):
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']  # 支持中文
        plt.rcParams['axes.unicode_minus'] = False
        self.node_data = {}  # 存储节点数据
        self.on_node_click = None  # 节点点击回调函数
    
    def create_network_graph(self, items: List[Dict], parent_widget, on_node_click=None) -> FigureCanvasTkAgg:
        """创建可交互的网点图"""
        fig, ax = plt.subplots(figsize=(10, 8))

        # 存储回调函数
        self.on_node_click = on_node_click

        # 创建图
        G = nx.Graph()

        # 按分类分组
        categories = {}
        for item in items:
            category = item['category']
            if category not in categories:
                categories[category] = []
            categories[category].append(item)

        # 添加节点和边
        pos = {}
        colors = []
        labels = {}
        self.node_data = {}  # 清空节点数据

        # 为每个分类分配颜色
        category_colors = plt.cm.Set3(range(len(categories)))
        color_map = {cat: color for cat, color in zip(categories.keys(), category_colors)}

        # 添加分类中心节点
        center_y = 0
        for i, (category, cat_items) in enumerate(categories.items()):
            # 分类中心节点
            center_node = f"分类_{category}"
            G.add_node(center_node)
            pos[center_node] = (0, center_y)
            colors.append(color_map[category])
            labels[center_node] = category

            # 存储分类节点数据
            self.node_data[center_node] = {
                'type': 'category',
                'name': category,
                'items': cat_items
            }

            # 添加该分类下的题目节点
            angle_step = 2 * 3.14159 / max(len(cat_items), 1)
            radius = 2

            for j, item in enumerate(cat_items):
                node_id = f"item_{item['id']}"
                angle = j * angle_step
                x = radius * plt.np.cos(angle)
                y = center_y + radius * plt.np.sin(angle)

                G.add_node(node_id)
                G.add_edge(center_node, node_id)
                pos[node_id] = (x, y)
                colors.append(color_map[category])

                # 截断长标题
                title = item['title']
                if len(title) > 10:
                    title = title[:10] + "..."
                labels[node_id] = title

                # 存储节点数据
                self.node_data[node_id] = {
                    'type': 'item',
                    'data': item
                }

            center_y += 5  # 分类间距

        # 绘制图
        nx.draw(G, pos, node_color=colors, labels=labels,
                with_labels=True, node_size=1000, font_size=8,
                font_family='SimHei', ax=ax)

        ax.set_title("知识网点图 (点击节点查看详情)", fontsize=16, fontfamily='SimHei')
        plt.tight_layout()

        # 创建画布并绑定事件
        canvas = FigureCanvasTkAgg(fig, parent_widget)
        canvas.mpl_connect('button_press_event', self._on_canvas_click)
        canvas.mpl_connect('motion_notify_event', self._on_mouse_move)

        # 存储位置信息用于点击检测
        self.pos = pos
        self.ax = ax
        self.fig = fig
        self.canvas = canvas

        return canvas

    def _on_canvas_click(self, event):
        """处理画布点击事件"""
        if event.inaxes != self.ax or not self.on_node_click:
            return

        # 获取点击位置
        click_x, click_y = event.xdata, event.ydata
        if click_x is None or click_y is None:
            return

        # 查找最近的节点
        min_distance = float('inf')
        closest_node = None

        for node, (x, y) in self.pos.items():
            distance = ((click_x - x) ** 2 + (click_y - y) ** 2) ** 0.5
            if distance < min_distance and distance < 0.5:  # 点击范围阈值
                min_distance = distance
                closest_node = node

        # 如果找到节点，触发回调
        if closest_node and closest_node in self.node_data:
            node_info = self.node_data[closest_node]
            if node_info['type'] == 'item':
                self.on_node_click(node_info['data'])
            elif node_info['type'] == 'category':
                # 可以实现分类节点的特殊处理
                pass

    def _on_mouse_move(self, event):
        """处理鼠标移动事件，显示悬停效果"""
        if event.inaxes != self.ax:
            return

        # 获取鼠标位置
        mouse_x, mouse_y = event.xdata, event.ydata
        if mouse_x is None or mouse_y is None:
            return

        # 查找鼠标悬停的节点
        hover_node = None
        for node, (x, y) in self.pos.items():
            distance = ((mouse_x - x) ** 2 + (mouse_y - y) ** 2) ** 0.5
            if distance < 0.5:  # 悬停范围阈值
                hover_node = node
                break

        # 更新鼠标样式和提示
        if hover_node and hover_node in self.node_data:
            # 设置鼠标为手型
            self.canvas.get_tk_widget().configure(cursor="hand2")

            # 可以在这里添加工具提示
            node_info = self.node_data[hover_node]
            if node_info['type'] == 'item':
                title = node_info['data']['title']
                self.ax.set_title(f"知识网点图 - {title} (点击查看详情)", fontsize=16, fontfamily='SimHei')
            elif node_info['type'] == 'category':
                category = node_info['name']
                count = len(node_info['items'])
                self.ax.set_title(f"知识网点图 - {category} ({count}项)", fontsize=16, fontfamily='SimHei')

            self.canvas.draw_idle()
        else:
            # 恢复默认鼠标样式
            self.canvas.get_tk_widget().configure(cursor="")
            self.ax.set_title("知识网点图 (点击节点查看详情)", fontsize=16, fontfamily='SimHei')
            self.canvas.draw_idle()
    
    def create_mind_map(self, items: List[Dict], parent_widget, on_node_click=None) -> FigureCanvasTkAgg:
        """创建可交互的思维导图"""
        fig, ax = plt.subplots(figsize=(12, 8))

        # 存储回调函数
        self.on_node_click = on_node_click

        # 创建有向图
        G = nx.DiGraph()

        # 按分类分组
        categories = {}
        for item in items:
            category = item['category']
            if category not in categories:
                categories[category] = []
            categories[category].append(item)

        # 添加根节点
        root = "知识库"
        G.add_node(root)

        # 使用层次布局
        pos = {root: (0, 0)}
        colors = [plt.cm.Set1(0)]
        labels = {root: root}
        self.node_data = {}  # 清空节点数据

        # 根节点数据
        self.node_data[root] = {
            'type': 'root',
            'name': '知识库'
        }

        # 第一层：分类
        category_y_positions = []
        if len(categories) > 1:
            step = 4 / (len(categories) - 1) if len(categories) > 1 else 0
            category_y_positions = [-2 + i * step for i in range(len(categories))]
        else:
            category_y_positions = [0]

        category_colors = plt.cm.Set3(range(len(categories)))

        for i, (category, cat_items) in enumerate(categories.items()):
            cat_node = f"cat_{category}"
            G.add_node(cat_node)
            G.add_edge(root, cat_node)

            y_pos = category_y_positions[i] if i < len(category_y_positions) else 0
            pos[cat_node] = (2, y_pos)
            colors.append(category_colors[i])
            labels[cat_node] = category

            # 存储分类节点数据
            self.node_data[cat_node] = {
                'type': 'category',
                'name': category,
                'items': cat_items
            }

            # 第二层：题目
            if cat_items:
                item_step = 1.5 / len(cat_items) if len(cat_items) > 1 else 0
                item_y_start = y_pos - 0.75

                for j, item in enumerate(cat_items):
                    item_node = f"item_{item['id']}"
                    G.add_node(item_node)
                    G.add_edge(cat_node, item_node)

                    item_y = item_y_start + j * item_step
                    pos[item_node] = (4, item_y)
                    colors.append(category_colors[i])

                    # 截断长标题
                    title = item['title']
                    if len(title) > 8:
                        title = title[:8] + "..."
                    labels[item_node] = title

                    # 存储节点数据
                    self.node_data[item_node] = {
                        'type': 'item',
                        'data': item
                    }

        # 绘制思维导图
        nx.draw(G, pos, node_color=colors, labels=labels,
                with_labels=True, node_size=800, font_size=8,
                font_family='SimHei', arrows=True, arrowsize=20,
                edge_color='gray', ax=ax)

        ax.set_title("知识思维导图 (点击节点查看详情)", fontsize=16, fontfamily='SimHei')
        plt.tight_layout()

        # 创建画布并绑定事件
        canvas = FigureCanvasTkAgg(fig, parent_widget)
        canvas.mpl_connect('button_press_event', self._on_canvas_click)
        canvas.mpl_connect('motion_notify_event', self._on_mouse_move)

        # 存储位置信息用于点击检测
        self.pos = pos
        self.ax = ax
        self.fig = fig
        self.canvas = canvas

        return canvas
    
    def show_category_distribution(self, items: List[Dict], parent_widget) -> FigureCanvasTkAgg:
        """显示分类分布饼图"""
        fig, ax = plt.subplots(figsize=(8, 6))
        
        # 统计各分类的数量
        category_counts = {}
        for item in items:
            category = item['category']
            category_counts[category] = category_counts.get(category, 0) + 1
        
        if category_counts:
            labels = list(category_counts.keys())
            sizes = list(category_counts.values())
            colors = plt.cm.Set3(range(len(labels)))
            
            # 绘制饼图
            wedges, texts, autotexts = ax.pie(sizes, labels=labels, colors=colors, 
                                            autopct='%1.1f%%', startangle=90)
            
            # 设置字体
            for text in texts:
                text.set_fontfamily('SimHei')
            for autotext in autotexts:
                autotext.set_fontfamily('SimHei')
                autotext.set_color('white')
                autotext.set_weight('bold')
        
        ax.set_title("知识分类分布", fontsize=16, fontfamily='SimHei')
        
        # 创建画布
        canvas = FigureCanvasTkAgg(fig, parent_widget)
        return canvas
