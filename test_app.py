#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本 - 添加一些示例数据并启动应用
"""

from database import DatabaseManager
from main import KnowledgeBaseApp

def add_sample_data():
    """添加示例数据"""
    db = DatabaseManager()
    
    # 示例数据（支持多分类）
    sample_data = [
        {
            "category": "Python编程",
            "title": "什么是列表推导式？",
            "content": "列表推导式是Python中创建列表的简洁方式。语法：[expression for item in iterable if condition]",
            "other": "示例：[x**2 for x in range(10) if x%2==0]"
        },
        {
            "category": "Python编程, 高级特性",
            "title": "如何使用装饰器？",
            "content": "装饰器是修改函数行为的工具。使用@decorator语法或decorator(function)形式。",
            "other": "常用于日志记录、性能测试、权限检查等场景。"
        },
        {
            "category": "数据结构, 算法基础",
            "title": "什么是二叉树？",
            "content": "二叉树是每个节点最多有两个子节点的树结构，分为左子树和右子树。",
            "other": "常见类型：完全二叉树、满二叉树、平衡二叉树等。"
        },
        {
            "category": "数据结构",
            "title": "栈和队列的区别？",
            "content": "栈是后进先出(LIFO)的数据结构，队列是先进先出(FIFO)的数据结构。",
            "other": "栈用于函数调用、表达式求值；队列用于任务调度、广度优先搜索。"
        },
        {
            "category": "算法, 排序算法",
            "title": "什么是快速排序？",
            "content": "快速排序是分治算法，选择基准元素，将数组分为小于和大于基准的两部分，递归排序。",
            "other": "平均时间复杂度O(nlogn)，最坏情况O(n²)。"
        },
        {
            "category": "算法, 图算法",
            "title": "深度优先搜索DFS",
            "content": "DFS是图遍历算法，尽可能深地搜索图的分支，使用栈或递归实现。",
            "other": "应用：路径查找、拓扑排序、连通性检测。"
        },
        {
            "category": "数据库, 性能优化",
            "title": "什么是SQL索引？",
            "content": "索引是数据库中用于快速查找数据的数据结构，类似书籍的目录。",
            "other": "提高查询速度，但会增加存储空间和写入开销。"
        },
        {
            "category": "数据库, 事务管理",
            "title": "ACID特性是什么？",
            "content": "ACID是数据库事务的四个特性：原子性(Atomicity)、一致性(Consistency)、隔离性(Isolation)、持久性(Durability)。",
            "other": "确保数据库事务的可靠性和完整性。"
        },
        {
            "category": "机器学习, Python编程",
            "title": "什么是梯度下降？",
            "content": "梯度下降是机器学习中的优化算法，通过计算损失函数的梯度来更新参数，使损失函数最小化。",
            "other": "包括批量梯度下降、随机梯度下降、小批量梯度下降等变种。"
        },
        {
            "category": "Web开发, 前端技术",
            "title": "什么是响应式设计？",
            "content": "响应式设计是一种网页设计方法，使网页能够在不同设备和屏幕尺寸上良好显示。",
            "other": "主要技术：CSS媒体查询、弹性布局、流式网格等。"
        }
    ]
    
    # 添加示例数据
    for item in sample_data:
        try:
            db.add_knowledge_item(
                item["category"],
                item["title"], 
                item["content"],
                item["other"]
            )
            print(f"已添加: {item['title']}")
        except Exception as e:
            print(f"添加失败 {item['title']}: {e}")
    
    print(f"\n示例数据添加完成！共添加 {len(sample_data)} 条记录。")

def main():
    """主函数"""
    print("智能知识管理系统 - 测试启动")
    print("=" * 50)
    
    # 检查是否需要添加示例数据
    db = DatabaseManager()
    items = db.get_knowledge_items()
    
    if len(items) == 0:
        print("检测到数据库为空，正在添加示例数据...")
        add_sample_data()
    else:
        print(f"数据库中已有 {len(items)} 条记录")
    
    print("\n启动应用程序...")
    print("=" * 50)
    
    # 启动应用
    app = KnowledgeBaseApp()
    app.run()

if __name__ == "__main__":
    main()
